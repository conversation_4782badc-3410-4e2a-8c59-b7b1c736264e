# 进度条优化方案：回调时只更新进度，完成时更新数据

## 方案概述

修改了审计报告查询的实现方式：
- **回调过程中**：只发送进度信息，不发送表格数据
- **查询完成时**：一次性发送完整的表格数据

这样避免了频繁的DOM更新，提升了性能和用户体验。

## 后端实现

### 回调逻辑修改

```javascript
if (value.moreFollows === 0) {
  // 最后一次回调：发送完整数据
  const finalResult = new IECResult<AuditLogEntry[]>();
  finalResult.code = IECCONSTANTS.CODE_SUCCESS;
  finalResult.msg = t("errors.getSuccessful");
  finalResult.data = [...reportList]; // 发送完整数据

  const notify: IECNotify = {
    type: "readAuditLogReport",
    data: finalResult,
    deviceId: req.head.id,
    isPartial: false, // 最终数据
  };

  sendMessageToUIByNotify(IEC_EVENT.REPORT_NOTIFY, notify, getMainWindow());
} else {
  // 中间回调：只发送进度信息
  const progressResult = new IECResult<any>();
  progressResult.code = IECCONSTANTS.CODE_SUCCESS;
  progressResult.msg = t("errors.getSuccessful");
  progressResult.data = {
    progress: {
      currentCount: reportList.length,
      callbackCount: callbackCount,
      isProgress: true // 标识这是进度更新
    }
  };

  const notify: IECNotify = {
    type: "readAuditLogReport",
    data: progressResult,
    deviceId: req.head.id,
    isPartial: true, // 进度数据
  };

  sendMessageToUIByNotify(IEC_EVENT.REPORT_NOTIFY, notify, getMainWindow());
}
```

### 数据结构

**进度通知数据结构**:
```javascript
{
  code: 1,
  msg: "成功",
  data: {
    progress: {
      currentCount: 1200,    // 当前累积数据量
      callbackCount: 15,     // 回调次数
      isProgress: true       // 标识为进度更新
    }
  }
}
```

**最终数据通知结构**:
```javascript
{
  code: 1,
  msg: "成功",
  data: [
    // 完整的审计报告数据数组
    { id: 1, name: "...", time: "..." },
    // ...
  ]
}
```

## 前端实现

### 通知处理逻辑

```javascript
// 检查是否为进度更新
if (reportData.data?.progress?.isProgress) {
  // 这是进度更新，只更新进度条
  const progressInfo = reportData.data.progress;
  const currentCount = progressInfo.currentCount;
  const callbackCount = progressInfo.callbackCount;
  
  // 基于实际数据量计算进度
  let progressPercent = Math.min(85, Math.floor(Math.log(currentCount + 1) * 10 + callbackCount * 2));
  progressPercent = Math.max(dialogShow.percentage, progressPercent);
  
  dialogShow.percentage = progressPercent;
  dialogShow.progressText = `正在查询审计报告 (${currentCount} 条)`;
  return; // 不更新表格数据
}

// 这是最终数据更新
if (Array.isArray(reportData.data)) {
  // 一次性更新表格数据
  tableData.value = filteredList;
  forceEndLoading("最终数据处理完成");
}
```

### 进度计算优化

- **基于数据量**: 使用对数函数避免进度跳跃过快
- **基于回调次数**: 确保即使数据量少也有进度变化
- **进度不倒退**: 确保进度条只增不减
- **最大85%**: 为最终完成留出15%空间

## 性能优势

### 优化前的问题
- 每次回调都发送完整数据（可能几千条）
- 频繁的DOM更新导致界面卡顿
- 大量数据传输影响性能
- 用户看到数据闪烁更新

### 优化后的优势
- ✅ **减少数据传输**: 回调时只传输进度信息（几十字节）
- ✅ **避免频繁DOM更新**: 表格只在最终完成时更新一次
- ✅ **提升响应速度**: 进度条更新更流畅
- ✅ **更好的用户体验**: 没有数据闪烁，界面更稳定

## 用户体验改进

### 进度显示
- **实时进度**: 基于真实数据量的进度计算
- **数据计数**: 显示当前已处理的数据条数
- **平滑增长**: 进度条平滑增长，不会跳跃

### 数据展示
- **一次性加载**: 所有数据处理完成后一次性显示
- **无闪烁**: 避免了数据逐步加载时的闪烁效果
- **快速响应**: 最终数据显示更快

## 日志监控

### 后端日志
```
[AuditLog] 发送进度更新 - 当前数据量: 800条, 回调次数: 10
[AuditLog] 发送进度更新 - 当前数据量: 1200条, 回调次数: 15
[AuditLog] 发送最终数据 - 总数据量: 2048条
```

### 前端日志
```
[AuditLog UI] 进度更新完成 - 数据量: 800条, 回调次数: 10, 进度: 65%
[AuditLog UI] 进度更新完成 - 数据量: 1200条, 回调次数: 15, 进度: 78%
[AuditLog UI] 最终数据处理完成 - 最终数据量: 2048条
```

## 兼容性

- 保持了原有的API接口
- 保持了原有的错误处理机制
- 新增的进度信息向后兼容

## 测试验证

1. **小数据量测试**: 验证少量数据时的进度显示
2. **大数据量测试**: 验证大量数据时的性能提升
3. **网络延迟测试**: 验证网络较慢时的用户体验
4. **错误处理测试**: 验证异常情况下的处理

这个优化方案显著提升了审计报告查询的性能和用户体验，特别是在处理大量数据时效果更加明显。
