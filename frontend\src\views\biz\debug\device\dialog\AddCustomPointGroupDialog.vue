<template>
  <el-dialog v-model="visible" width="900px">
    <template #title>
      <svg-icon icon="ep:collection" style="margin-right: 6px; color: var(--el-color-primary); vertical-align: middle" />
      {{ isEdit ? t("device.customMenu.editPointGroup") : t("device.customMenu.addPointGroup") }}
    </template>
    <div class="dialog-body">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px" class="left-form">
        <el-form-item :label="t('device.customMenu.menuName')" prop="name">
          <el-input v-model="form.name" :placeholder="t('device.customMenu.inputMenuName')" />
        </el-form-item>
        <el-form-item :label="t('device.customMenu.menuDesc')" prop="desc">
          <el-input v-model="form.desc" type="textarea" :placeholder="t('device.customMenu.inputMenuDesc')" />
        </el-form-item>
        <el-form-item :label="t('device.customMenu.selectFc')" prop="fc">
          <el-select v-model="form.fc" filterable clearable style="width: 100%" @change="onFcChange">
            <el-option v-for="fc in fcList" :key="fc" :label="fc" :value="fc" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="right-selector">
        <div class="selector-header">
          <el-input v-model="filterText" :placeholder="t('device.customMenu.filterPlaceholder')" clearable />
        </div>
        <div class="selector-content">
          <div class="left">
            <el-tabs v-model="activeTab" type="card">
              <el-tab-pane v-for="tab in fcTabs" :key="tab.name" :label="tab.desc || tab.name" :name="tab.name">
                <el-table :data="filteredItems(tab.items)" height="320" @row-dblclick="onAddItem">
                  <el-table-column type="index" width="50" />
                  <el-table-column prop="name" :label="t('device.groupInfo.table.name')" width="220" />
                  <el-table-column prop="desc" :label="t('device.groupInfo.table.desc')" />
                  <el-table-column width="80">
                    <template #default="{ row }">
                      <el-button link type="primary" @click="onAddItem(row)">{{ t("common.add") }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="mid">
            <el-button type="primary" plain :disabled="!candidateToAdd" @click="onAddItem(candidateToAdd)">→</el-button>
            <el-button type="danger" plain :disabled="!candidateToRemove" @click="onRemoveItem(candidateToRemove)">←</el-button>
          </div>
          <div class="right">
            <div class="title">{{ t("device.customMenu.selectedPoints") }} ({{ selectedItems.length }})</div>
            <el-table :data="selectedItems" height="360" @row-dblclick="onRemoveItem">
              <el-table-column type="index" width="50" />
              <el-table-column prop="name" :label="t('device.groupInfo.table.name')" width="220" />
              <el-table-column prop="desc" :label="t('device.groupInfo.table.desc')" />
              <el-table-column width="80">
                <template #default="{ row }">
                  <el-button link type="danger" @click="onRemoveItem(row)">{{ t("common.remove") }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="onClose">{{ t("device.customMenu.cancel") }}</el-button>
      <el-button type="primary" @click="onConfirm">{{ t("device.customMenu.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { customInfoApi, type SelectedItem, type CustomGroup } from "@/api/modules/biz/debug/custominfo";
import { getUUID } from "@/utils";
import { useDebugStore } from "@/stores/modules/debug";
const { t } = useI18n();
const { currDevice } = useDebugStore();

const emit = defineEmits<{ (e: "confirm", group: CustomGroup, isEdit: boolean): void }>();
const visible = defineModel<boolean>("visible", { default: false });
const isEdit = defineModel<boolean>("isEdit", { default: false });

const modelValue = defineModel<CustomGroup | null>("model", { default: null });

const formRef = ref();
const form = reactive<{ uuid?: string; name: string; desc: string; fc?: string }>({ name: "", desc: "", fc: undefined });
const rules = {
  name: [{ required: true, message: t("device.customMenu.inputMenuName"), trigger: "blur" }],
  desc: [{ required: true, message: t("device.customMenu.inputMenuDesc"), trigger: "blur" }],
  fc: [{ required: true, message: t("device.customMenu.selectFc"), trigger: "change" }]
};

const fcList = ref<string[]>([]);
const fcTabs = ref<{ name: string; desc: string; items: SelectedItem[] }[]>([]);
const activeTab = ref<string>("");
const filterText = ref("");
const selectedItems = ref<SelectedItem[]>([]);
const candidateToAdd = ref<SelectedItem | null>(null);
const candidateToRemove = ref<SelectedItem | null>(null);

watch(visible, async v => {
  if (v) {
    await loadFcList();
    if (isEdit.value && modelValue.value) {
      form.uuid = modelValue.value.uuid;
      form.name = modelValue.value.name || "";
      form.desc = modelValue.value.desc || "";
      form.fc = modelValue.value.fc || undefined;
      selectedItems.value = Array.isArray(modelValue.value.items) ? [...modelValue.value.items] : [];
      if (form.fc) await onFcChange(form.fc);
    } else {
      form.uuid = getUUID();
      form.name = "";
      form.desc = "";
      form.fc = undefined;
      selectedItems.value = [];
      activeTab.value = "";
    }
  }
});

async function loadFcList() {
  try {
    const deviceId = currDevice.id;
    const res = await customInfoApi.getFcListByDevice(deviceId);
    if (res.code === 0) fcList.value = res.data || [];
  } catch (e) {
    console.error("getFcListByDevice failed", e);
  }
}

async function onFcChange(value?: string) {
  if (!value) return;
  try {
    const deviceId = currDevice.id;
    const res = await customInfoApi.getMenusByFcByDevice(deviceId, value);
    if (res.code === 0) {
      const menus = Array.isArray(res.data) ? res.data : [];
      fcTabs.value = menus.map((m: any) => ({
        name: m.name,
        desc: m.desc,
        items: (m.items || []).map((it: any) => ({
          name: it.name,
          desc: it.desc,
          grp: it.grp,
          inf: it.inf,
          fc: m.fc || value,
          unit: it.unit,
          type: it.type
        }))
      }));
      activeTab.value = fcTabs.value[0]?.name || "";
    }
  } catch (e) {
    console.error("getMenusByFcByDevice failed", e);
  }
}

function filteredItems(items: SelectedItem[]) {
  if (!filterText.value) return items.filter(i => !selectedItems.value.some(s => s.name === i.name));
  const text = filterText.value.toLowerCase();
  return items.filter(
    i => (i.name?.toLowerCase()?.includes(text) || i.desc?.toLowerCase()?.includes(text)) && !selectedItems.value.some(s => s.name === i.name)
  );
}

function onAddItem(row: SelectedItem | null) {
  if (!row) return;
  if (!selectedItems.value.some(i => i.name === row.name)) {
    selectedItems.value.push({ ...row });
  }
}

function onRemoveItem(row: SelectedItem) {
  if (!row) return;
  selectedItems.value = selectedItems.value.filter(i => i.name !== row.name);
}

function onClose() {
  visible.value = false;
}

async function onConfirm() {
  try {
    await formRef.value.validate();
    const payload: CustomGroup = {
      uuid: form.uuid || getUUID(),
      name: form.name,
      desc: form.desc,
      fc: "customGroup",
      keyword: undefined as any,
      inherit: undefined as any,
      items: selectedItems.value
    } as any;
    // 通过 emit 通知父组件提交（父组件调用 addMenu/editMenu）
    emit("confirm", payload, isEdit.value);
    onClose();
  } catch (e) {
    ElMessage.error(t("device.customMenu.errorAction"));
  }
}

// moved defineEmits to top to avoid redeclaration
</script>

<style scoped>
.dialog-body {
  display: flex;
  gap: 12px;
}
.left-form {
  width: 320px;
}
.right-selector {
  display: flex;
  flex: 1;
  flex-direction: column;
}
.selector-header {
  margin-bottom: 8px;
}
.selector-content {
  display: flex;
  gap: 8px;
}
.selector-content .left {
  flex: 1;
}
.selector-content .mid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 60px;
}
.selector-content .right {
  flex: 1;
}
.title {
  margin: 6px 0;
  font-weight: 600;
}
</style>
