<template>
  <div class="containerComponent">
    <el-collapse v-model="activeCollapse">
      <el-collapse-item v-for="(item, index) in componentArrs" :name="index" :key="index" :title="t(item.title)">
        <el-row :gutter="10">
          <el-col
            v-for="(componentItem, index1) in item.components"
            :span="6"
            :key="index1"
            draggable="true"
            @dragstart="componentToolClick($event, componentItem)"
            :class="getEquipmentClass(index)"
          >
            <el-image :src="componentItem.url" :alt="t(componentItem.title)" :title="t(componentItem.title)" class="graph-equipment-img">
              <template #error>
                {{ t(componentItem.title) }}
              </template>
            </el-image>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script setup lang="tsx">
import { onMounted, ref, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ComponentInfo, EquipmentData } from "../../../graph/Graph";
import { getBasicComponent } from "../..";

const { t } = useI18n();

const prop = defineProps<{
  // 自定义的设备符号
  equipmentDatas: EquipmentData[];
  // 电气符号
  electricDatas: unknown[];
}>();
const emit = defineEmits<{
  (e: "addComponent", event: MouseEvent, componentInfo: ComponentInfo): void;
}>();

const images = import.meta.glob("@/assets/hmi/images/*.png", { eager: true });
onMounted(async () => {
  // 初始化自定义图符
  initEquipmentComponents(prop.equipmentDatas);
});
watch(prop, () => {
  // 电气符号
  setEquipmentFigure(prop.electricDatas as EquipmentData[]);
  initEquipmentComponents(prop.equipmentDatas);
});
const activeCollapse = ref(0);

// 使用ref来管理组件数组的动态部分
const electricComponents = ref<ComponentInfo[]>([]);
const customComponents = ref<ComponentInfo[]>([]);

// 使用computed确保组件数组能够响应语言变化
const componentArrs = computed(() => {
  const arrs: { title: string; components?: ComponentInfo[] }[] = [
    { title: "hmi.graph.component.electricSymbols", components: electricComponents.value },
    { title: "hmi.graph.component.customComponents", components: customComponents.value }
  ];
  arrs.unshift(getBasicComponent(t));
  return arrs;
});
const initEquipmentComponents = (datas: EquipmentData[]) => {
  const components: ComponentInfo[] = [];
  const equipmentList = datas;
  for (const item of equipmentList) {
    if (!item.components) {
      continue;
    }
    let index = 0;
    for (const component of item.components) {
      const c: ComponentInfo = {
        title: item.name,
        url: component.img,
        data: { ...component.data.value },
        equipmentData: item,
        cellEquipmentData: {
          equipmentId: item.id,
          equipmentIndex: index++,
          equipmentStatus: component.equipmentStatus,
          equipmentType: item.type
        }
      };
      components.push(c);
    }
  }
  customComponents.value = components;
};
const setEquipmentFigure = (datas: EquipmentData[]) => {
  const components: ComponentInfo[] = [];
  const equipmentList = datas;
  for (const item of equipmentList) {
    if (!item.components) {
      continue;
    }
    let index = 0;
    for (const component of item.components) {
      // new URL("src/assets/hmi/images/" + component.img + ".png", baseUrl).href
      const fileName = `/src/assets/hmi/images/${component.img}.png`;
      let url = images[fileName];
      if (url) {
        url = (url as any).default;
      } else {
        url = "";
      }
      const c: ComponentInfo = {
        title: item.name,
        url: url as string,
        data: { ...component.data.value },
        cellEquipmentData: {
          equipmentId: item.id,
          equipmentIndex: index++,
          equipmentStatus: component.equipmentStatus,
          equipmentType: item.type,
          loadType: "create"
        }
      };
      components.push(c);
    }
  }
  electricComponents.value = components;
};
const componentToolClick = (event: MouseEvent, componentItem: ComponentInfo) => {
  emit("addComponent", event, componentItem);
};
const getEquipmentClass = (index: number) => {
  if (index == 0) {
    return "graph-basic-item";
  } else if (index == 1) {
    return "graph-electric-item";
  }
  return "graph-equipment-item";
};
</script>
<style>
@import "../../../styles/Graph";
.containerComponent {
  padding: 0 0 0 10px;
}
</style>
