# ESLint 自动格式化配置指南

## 配置完成 ✅

已经为您的工程配置了保存时自动进行ESLint格式化的功能。

## 配置内容

### 1. VSCode 设置 (`.vscode/settings.json`)

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.enable": true,
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"],
  "eslint.format.enable": true,
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  }
}
```

### 2. 工作流程

1. **保存时自动格式化**: 当您按 `Ctrl+S` 保存文件时，会自动执行：
   - ESLint 自动修复可修复的问题
   - Prettier 格式化代码样式

2. **支持的文件类型**:
   - `.vue` 文件
   - `.js` / `.ts` 文件
   - `.jsx` / `.tsx` 文件

### 3. 手动命令

如果需要手动运行格式化：

```bash
# 进入前端目录
cd frontend

# 运行ESLint修复
npm run lint:eslint

# 运行Prettier格式化
npm run lint:prettier

# 运行所有格式化
npm run lint:lint-staged
```

## 测试配置

运行测试脚本验证配置是否正常：

```bash
node scripts/test-eslint.js
```

## 使用说明

### 自动格式化
1. 在VSCode中打开任意Vue文件
2. 修改代码（比如添加多余的空格、错误的缩进等）
3. 按 `Ctrl+S` 保存
4. 观察代码是否自动格式化

### 常见问题解决

#### 1. ESLint不工作
- 确保安装了 `ESLint` 扩展
- 重启VSCode
- 检查控制台是否有错误信息

#### 2. 格式化冲突
- ESLint和Prettier可能有冲突
- 已配置 `eslint-config-prettier` 来解决冲突

#### 3. Vue文件格式化问题
- 确保安装了 `Vue - Official` (Volar) 扩展
- 模板部分由Prettier格式化
- 脚本部分由ESLint格式化

## 配置文件位置

- **ESLint配置**: `frontend/.eslintrc.cjs`
- **Prettier配置**: `frontend/.prettierrc.cjs`
- **VSCode配置**: `.vscode/settings.json`

## 自定义规则

如需修改格式化规则，请编辑：

1. **ESLint规则**: 修改 `frontend/.eslintrc.cjs` 中的 `rules` 部分
2. **Prettier规则**: 修改 `frontend/.prettierrc.cjs` 中的配置

## 团队协作

所有团队成员都会使用相同的格式化规则，确保代码风格一致。

## 验证效果

打开 `frontend/src/views/biz/debug/device/views/ReportAuditLog.vue` 文件：
1. 故意添加一些格式问题（多余空格、错误缩进等）
2. 保存文件 (`Ctrl+S`)
3. 观察代码是否自动修复

如果自动格式化正常工作，说明配置成功！
